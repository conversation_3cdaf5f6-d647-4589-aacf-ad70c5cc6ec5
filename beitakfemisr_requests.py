import sys
import json
import requests
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QLineEdit,
                             QComboBox, QTextEdit, QSpinBox, QListWidget, QMessageBox,
                             QGroupBox, QFrame, QTabWidget, QSizePolicy, QScrollArea)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QSize
from PyQt5.QtGui import QFont
import time
import elitesoftworks

class BookingWorker(QThread):
    log_signal = pyqtSignal(str)
    
    def __init__(self, attempts, delay, auth_data, service_slug):
        super().__init__()
        self.attempts = attempts
        self.delay = delay
        self.auth_data = auth_data
        self.service_slug = service_slug
        self.running = True
        self.attempt_counter = 0
        
    def run(self):
        while self.running:
            for idx, attempt in enumerate(self.attempts):
                if not self.running:
                    break
                    
                self.attempt_counter += 1
                self.log_signal.emit(f"\n--- المحاولة #{self.attempt_counter} ---")
                self.log_signal.emit(f"الوحدة: {attempt['display']}")
                
                try:
                    # Get unit details first
                    unit_details = self.get_unit_details(attempt)
                    if not unit_details:
                        self.log_signal.emit("❌ فشل الحصول على تفاصيل الوحدة")
                        continue

                    # Log unit details
                    self.log_signal.emit("✅ تم الحصول على تفاصيل الوحدة:")
                    self.log_signal.emit(f"   كود الوحدة: {unit_details.get('unitCode', 'غير متوفر')}")
                    self.log_signal.emit(f"   المساحة: {unit_details.get('area', 'غير متوفر')} متر مربع")
                    self.log_signal.emit(f"   عدد الغرف: {unit_details.get('roomCount', 'غير متوفر')}")
                    self.log_signal.emit(f"   سعر المتر: {unit_details.get('pricePerMeter', 'غير متوفر')} جنيه")
                    self.log_signal.emit(f"   رسوم الحجز المتبقية: ${unit_details.get('unitRemainingReservationFeesUSD', 'غير متوفر')}")
                    self.log_signal.emit(f"   الرسوم الإدارية: ${unit_details.get('adminFeesUSD', 'غير متوفر')}")

                    # Attempt booking
                    success = self.book_unit(unit_details, attempt)
                    if success:
                        self.log_signal.emit("✅ تم الحجز بنجاح!")
                        self.running = False
                        return
                    else:
                        self.log_signal.emit("❌ فشل الحجز")

                except Exception as e:
                    self.log_signal.emit(f"❌ خطأ: {str(e)}")
                
                # Wait before next attempt
                if self.running:
                    self.log_signal.emit(f"⏳ انتظار {self.delay} ثانية...")
                    time.sleep(self.delay)
    
    def get_unit_details(self, attempt):
        url = f"https://api.beitakfemisr.com/api/{self.service_slug}/fieldHandler"
        payload = {
            "fieldId": "selectUnit",
            "data": {
                "model": attempt['model'],
                "buildingNumber": attempt['buildingNumber'],
                "floor": attempt['floor'],
                "unitNumber": attempt['unitNumber'],
                "alternativeCompletion": attempt['alternativeCompletion']
            },
            "activityId": "unitSelect"
        }
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'ar',
            'content-type': 'application/json',
            'origin': 'https://beitakfemisr.com',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'x-csrf-token': self.auth_data['userToken'],
            'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
        }
        
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            unit_details = response.json()
            self.log_signal.emit(f"استجابة تفاصيل الوحدة: {response.status_code}")
            return unit_details
        else:
            self.log_signal.emit(f"❌ فشل الحصول على تفاصيل الوحدة: {response.status_code}")
            self.log_signal.emit(f"نص الاستجابة: {response.text[:200]}")
        return None
    
    def book_unit(self, unit_details, attempt):
        url = f"https://api.beitakfemisr.com/api/{self.service_slug}"

        payload = {
            "activityId": "unitSelect",
            "residenceCountry": "AZE(+994)",
            "homeType": "unit",
            "selectUnit": [unit_details],
            "reservationRequest": attempt['reservationRequest'],
            "serviceSlug": self.service_slug
        }

        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'ar',
            'content-type': 'application/json',
            'device': 'CITIZEN',
            'origin': 'https://beitakfemisr.com',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'x-csrf-token': self.auth_data['userToken'],
            'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
        }

        response = requests.post(url, headers=headers, json=payload)
        self.log_signal.emit(f"كود الاستجابة: {response.status_code}")
        self.log_signal.emit(f"استجابة الحجز: {response.text}")

        if response.status_code == 200 or response.status_code == 201:
            return True
        return False
    
    def stop(self):
        self.running = False


class BookingBot(QMainWindow):
    def __init__(self):
        super().__init__()
        self.auth_data = None
        self.services = []
        self.attempts = []
        self.worker = None
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('بوت حجز بيتك في مصر')
        self.setGeometry(100, 100, 1100, 850)
        self.setMinimumSize(900, 700)  # Minimum size to prevent content cutoff
        self.resize(1100, 850)  # Default size

        # Main widget with tab widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_widget.setLayout(main_layout)

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        main_layout.addWidget(self.tab_widget)

        # Main tab with scroll area
        main_tab = QWidget()
        self.tab_widget.addTab(main_tab, "الواجهة الرئيسية")
        main_tab_layout = QVBoxLayout()
        main_tab_layout.setContentsMargins(0, 0, 0, 0)
        main_tab.setLayout(main_tab_layout)

        # Create scroll area for main content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        main_tab_layout.addWidget(scroll_area)

        # Content widget inside scroll area
        content_widget = QWidget()
        scroll_area.setWidget(content_widget)
        main_tab_layout = QVBoxLayout()
        main_tab_layout.setSpacing(10)  # Reduced spacing for cleaner look
        main_tab_layout.setContentsMargins(15, 15, 15, 15)  # Reduced margins
        content_widget.setLayout(main_tab_layout)

        # Logs tab
        logs_tab = QWidget()
        self.tab_widget.addTab(logs_tab, "السجل")
        logs_tab_layout = QVBoxLayout()
        logs_tab_layout.setSpacing(8)  # Reduced spacing
        logs_tab_layout.setContentsMargins(15, 15, 15, 15)  # Reduced margins
        logs_tab.setLayout(logs_tab_layout)
        
        # Login section
        login_group = QGroupBox('تسجيل الدخول')
        login_group.setMinimumHeight(100)
        login_layout = QVBoxLayout()
        login_layout.setSpacing(8)  # Reduced spacing

        login_fields = QHBoxLayout()
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText('اسم المستخدم')
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('كلمة المرور')
        self.password_input.setEchoMode(QLineEdit.Password)

        login_fields.addWidget(self.username_input)
        login_fields.addWidget(self.password_input)

        self.login_btn = QPushButton('🔐 تسجيل الدخول')
        self.login_btn.clicked.connect(self.login)
        self.login_btn.setCursor(Qt.PointingHandCursor)

        login_layout.addLayout(login_fields)
        login_layout.addWidget(self.login_btn)
        login_group.setLayout(login_layout)
        main_tab_layout.addWidget(login_group)
        
        # User info
        self.user_info_label = QLabel('')
        self.user_info_label.setAlignment(Qt.AlignCenter)
        main_tab_layout.addWidget(self.user_info_label)

        # Service selection
        service_group = QGroupBox('اختيار الخدمة')
        service_group.setMinimumHeight(70)
        service_layout = QVBoxLayout()
        service_layout.setSpacing(6)  # Reduced spacing
        self.service_combo = QComboBox()
        self.service_combo.currentIndexChanged.connect(self.on_service_changed)
        service_layout.addWidget(self.service_combo)
        service_group.setLayout(service_layout)
        main_tab_layout.addWidget(service_group)
        
        # Field selectors
        fields_group = QGroupBox('تفاصيل الوحدة')
        fields_group.setMinimumHeight(180)
        fields_main_layout = QVBoxLayout()
        fields_main_layout.setSpacing(8)  # Reduced spacing
        
        # Row 1
        row1 = QHBoxLayout()
        self.model_combo = QComboBox()
        self.building_combo = QComboBox()
        self.floor_combo = QComboBox()
        
        model_layout = QVBoxLayout()
        model_layout.addWidget(QLabel('النموذج'))
        model_layout.addWidget(self.model_combo)
        
        building_layout = QVBoxLayout()
        building_layout.addWidget(QLabel('المبنى'))
        building_layout.addWidget(self.building_combo)
        
        floor_layout = QVBoxLayout()
        floor_layout.addWidget(QLabel('الدور'))
        floor_layout.addWidget(self.floor_combo)
        
        row1.addLayout(model_layout)
        row1.addLayout(building_layout)
        row1.addLayout(floor_layout)
        
        # Row 2
        row2 = QHBoxLayout()
        self.unit_combo = QComboBox()
        self.alternative_combo = QComboBox()
        self.reservation_combo = QComboBox()
        
        unit_layout = QVBoxLayout()
        unit_layout.addWidget(QLabel('الوحدة'))
        unit_layout.addWidget(self.unit_combo)
        
        alternative_layout = QVBoxLayout()
        alternative_layout.addWidget(QLabel('الاستكمال'))
        alternative_layout.addWidget(self.alternative_combo)
        
        reservation_layout = QVBoxLayout()
        reservation_layout.addWidget(QLabel('طلب الحجز'))
        reservation_layout.addWidget(self.reservation_combo)
        
        row2.addLayout(unit_layout)
        row2.addLayout(alternative_layout)
        row2.addLayout(reservation_layout)
        
        fields_main_layout.addLayout(row1)
        fields_main_layout.addLayout(row2)
        
        # Add attempt button
        self.add_btn = QPushButton('➕ إضافة محاولة')
        self.add_btn.clicked.connect(self.add_attempt)
        self.add_btn.setCursor(Qt.PointingHandCursor)
        fields_main_layout.addWidget(self.add_btn)

        fields_group.setLayout(fields_main_layout)
        main_tab_layout.addWidget(fields_group)
        
        # Attempts section
        attempts_group = QGroupBox('المحاولات')
        attempts_group.setMinimumHeight(160)
        attempts_layout = QVBoxLayout()
        attempts_layout.setSpacing(6)  # Reduced spacing

        self.attempts_list = QListWidget()
        self.attempts_list.setMinimumHeight(100)  # Compact minimum height
        self.attempts_list.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        attempts_layout.addWidget(self.attempts_list)

        self.remove_btn = QPushButton('🗑️ حذف المحاولة المحددة')
        self.remove_btn.clicked.connect(self.remove_attempt)
        self.remove_btn.setCursor(Qt.PointingHandCursor)
        attempts_layout.addWidget(self.remove_btn)

        attempts_group.setLayout(attempts_layout)
        main_tab_layout.addWidget(attempts_group, 1)  # Give this section stretch factor
        
        # Control buttons
        control_group = QGroupBox('التحكم')
        control_group.setMinimumHeight(100)
        control_layout = QVBoxLayout()
        control_layout.setSpacing(6)  # Reduced spacing

        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel('التأخير بين المحاولات (ثواني):'))
        self.delay_spin = QSpinBox()
        self.delay_spin.setMinimum(1)
        self.delay_spin.setMaximum(60)
        self.delay_spin.setValue(2)
        delay_layout.addWidget(self.delay_spin)
        control_layout.addLayout(delay_layout)

        buttons_layout = QHBoxLayout()
        self.start_btn = QPushButton('▶️ بدء الحجز')
        self.start_btn.clicked.connect(self.start_booking)
        self.start_btn.setCursor(Qt.PointingHandCursor)

        self.stop_btn = QPushButton('⏹️ إيقاف')
        self.stop_btn.clicked.connect(self.stop_booking)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setCursor(Qt.PointingHandCursor)

        self.debug_btn = QPushButton('🔍 اختبار التفاصيل')
        self.debug_btn.clicked.connect(self.debug_unit_details)
        self.debug_btn.setCursor(Qt.PointingHandCursor)

        buttons_layout.addWidget(self.start_btn)
        buttons_layout.addWidget(self.stop_btn)
        buttons_layout.addWidget(self.debug_btn)
        control_layout.addLayout(buttons_layout)

        control_group.setLayout(control_layout)
        main_tab_layout.addWidget(control_group)
        
        # Log area in logs tab
        log_group = QGroupBox('السجل')
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(200)  # Minimum height for readability
        self.log_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        logs_tab_layout.addWidget(log_group, 1)  # Give full stretch to log area
        
        # Connect field changes
        self.model_combo.currentIndexChanged.connect(lambda: self.load_field_options('buildingNumber'))
        self.building_combo.currentIndexChanged.connect(lambda: self.load_field_options('floor'))
        self.floor_combo.currentIndexChanged.connect(lambda: self.load_field_options('unitNumber'))

        # Apply initial dynamic sizing
        self.update_dynamic_sizing()

    def resizeEvent(self, event):
        """Handle window resize events to update dynamic sizing"""
        super().resizeEvent(event)
        self.update_dynamic_sizing()

    def update_dynamic_sizing(self):
        """Update font sizes and button dimensions based on window size"""
        # Get current window size
        width = self.width()
        height = self.height()

        # Calculate base font size based on window dimensions with more conservative scaling
        # Ensure text remains readable at all sizes
        base_font_size = max(10, min(13, int((width + height) / 150)))

        # Calculate compact button dimensions for cleaner look
        button_height = max(28, min(36, int(height / 25)))
        button_padding_v = max(6, min(8, int(button_height / 4)))
        button_padding_h = max(12, min(16, int(button_height / 2)))

        # Update the stylesheet with dynamic values
        self.apply_dynamic_styles(base_font_size, button_height, button_padding_v, button_padding_h)

    def apply_dynamic_styles(self, base_font_size, button_height, button_padding_v, button_padding_h):
        """Apply styles with dynamic sizing"""
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #f5f7fa;
            }}

            QGroupBox {{
                font-weight: bold;
                font-size: {base_font_size + 1}px;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 12px;
                background-color: white;
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top right;
                padding: 4px 12px;
                color: #2c3e50;
            }}

            QLineEdit {{
                padding: {max(8, int(button_padding_v * 1.1))}px;
                border: 1px solid #e1e8ed;
                border-radius: 6px;
                background-color: #ffffff;
                font-size: {base_font_size}px;
                min-height: {max(18, int(button_height * 0.6))}px;
            }}

            QLineEdit:focus {{
                border: 2px solid #3498db;
            }}

            QComboBox {{
                padding: {max(6, int(button_padding_v * 0.8))}px;
                border: 1px solid #e1e8ed;
                border-radius: 6px;
                background-color: #ffffff;
                min-height: {max(24, int(button_height * 0.8))}px;
                font-size: {base_font_size}px;
            }}

            QComboBox:hover {{
                border: 2px solid #3498db;
            }}

            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}

            QSpinBox {{
                padding: {max(6, int(button_padding_v * 0.8))}px;
                border: 1px solid #e1e8ed;
                border-radius: 6px;
                background-color: #ffffff;
                min-height: {max(24, int(button_height * 0.8))}px;
                font-size: {base_font_size}px;
            }}

            QPushButton {{
                padding: {button_padding_v}px {button_padding_h}px;
                border: none;
                border-radius: 5px;
                font-weight: 600;
                font-size: {base_font_size}px;
                min-height: {button_height}px;
                max-width: 200px;
            }}

            QPushButton#login_btn, QPushButton:first-child {{
                background-color: #3498db;
                color: white;
            }}

            QPushButton:hover {{
                opacity: 0.9;
            }}

            QPushButton#add_btn {{
                background-color: #2ecc71;
                color: white;
            }}

            QPushButton#remove_btn {{
                background-color: #e74c3c;
                color: white;
            }}

            QPushButton#start_btn {{
                background-color: #27ae60;
                color: white;
            }}

            QPushButton#stop_btn {{
                background-color: #e74c3c;
                color: white;
            }}

            QPushButton#debug_btn {{
                background-color: #f39c12;
                color: white;
            }}

            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}

            QListWidget {{
                border: 1px solid #e1e8ed;
                border-radius: 6px;
                padding: 4px;
                background-color: #ffffff;
                font-size: {base_font_size}px;
            }}

            QListWidget::item {{
                padding: {max(6, int(button_padding_v * 1.0))}px;
                border-bottom: 1px solid #ecf0f1;
            }}

            QListWidget::item:selected {{
                background-color: #3498db;
                color: white;
            }}

            QTextEdit {{
                border: 1px solid #e1e8ed;
                border-radius: 6px;
                padding: 8px;
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Courier New', monospace;
                font-size: {max(9, base_font_size - 1)}px;
            }}

            QLabel {{
                color: #2c3e50;
                font-size: {base_font_size}px;
            }}

            QTabWidget::pane {{
                border: 1px solid #e1e8ed;
                border-radius: 6px;
                background-color: white;
            }}

            QTabBar::tab {{
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: {max(6, int(button_padding_v * 0.8))}px {max(12, int(button_padding_h * 0.8))}px;
                margin-right: 1px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                font-weight: 600;
                font-size: {base_font_size}px;
            }}

            QTabBar::tab:selected {{
                background-color: #3498db;
                color: white;
            }}

            QTabBar::tab:hover {{
                background-color: #bdc3c7;
            }}

            QScrollArea {{
                border: none;
                background-color: transparent;
            }}

            QScrollBar:vertical {{
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
            }}

            QScrollBar::handle:vertical {{
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }}

            QScrollBar::handle:vertical:hover {{
                background-color: #a0a0a0;
            }}
        """)

        # Set object names for specific styling
        self.login_btn.setObjectName("login_btn")
        self.add_btn.setObjectName("add_btn")
        self.remove_btn.setObjectName("remove_btn")
        self.start_btn.setObjectName("start_btn")
        self.stop_btn.setObjectName("stop_btn")
        self.debug_btn.setObjectName("debug_btn")

        # User info label special styling
        self.user_info_label.setStyleSheet(f"""
            QLabel {{
                background-color: #d5f4e6;
                color: #16a085;
                padding: {max(8, int(button_padding_v * 1.2))}px;
                border-radius: 6px;
                font-weight: 600;
                font-size: {base_font_size}px;
                border: 1px solid #1abc9c;
            }}
        """)


        
    def login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, 'خطأ', 'الرجاء إدخال اسم المستخدم وكلمة المرور')
            return
        
        self.log('جاري تسجيل الدخول...')
        
        try:
            url = "https://api.beitakfemisr.com/api/customer/login"
            payload = {"username": username, "password": password}
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'content-type': 'application/json',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'origin': 'https://beitakfemisr.com',
            }
            
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                self.auth_data = {
                    'userId': data['userId'],
                    'userSlug': data['profile']['userSlug'],
                    'userToken': response.headers['x-csrf-token'],
                    'cookies': response.cookies.get_dict(),
                    'profile': data['profile']
                }
                
                full_name = data['profile'].get('fullName', 'غير متوفر')
                national_id = data['profile'].get('nationalId', 'غير متوفر')
                nationality = data['profile'].get('nationality', 'غير متوفر')
                
                self.user_info_label.setText(
                    f'مرحباً {full_name} | الرقم القومي: {national_id} | الجنسية: {nationality}'
                )
                
                self.log('✅ تم تسجيل الدخول بنجاح')
                self.load_services()
            else:
                self.log(f'❌ فشل تسجيل الدخول: {response.status_code}')
                QMessageBox.warning(self, 'خطأ', 'فشل تسجيل الدخول')
                
        except Exception as e:
            self.log(f'❌ خطأ: {str(e)}')
            QMessageBox.critical(self, 'خطأ', str(e))

    def is_authenticated(self):
        """Check if user is properly authenticated"""
        if not self.auth_data:
            return False

        required_keys = ['userId', 'userToken', 'cookies']
        for key in required_keys:
            if key not in self.auth_data:
                return False

        return True

    def load_services(self):
        if not self.is_authenticated():
            self.log('❌ لم يتم تسجيل الدخول بشكل صحيح')
            return

        self.log('جاري تحميل الخدمات...')

        try:
            url = "https://api.beitakfemisr.com/api/Categories?filter=%7B%22queryKey%22:[%22categories%22],%22signal%22:%7B%7D,%22include%22:%7B%22relation%22:%22services%22,%22scope%22:%7B%22fields%22:[%22serviceDictionary%22,%22translations%22,%22activities%22,%22ID%22,%22personalization%22,%22categoryIds%22,%22createdAt%22,%22deactivated%22,%22id%22,%22logo%22,%22release%22,%22serviceLabel%22,%22description%22,%22serviceSlug%22,%22updatedAt%22,%22version%22,%22fromDate%22,%22toDate%22,%22hideServiceTable%22,%22hiddenOn%22,%22disabledOn%22,%22extraData%22,%22images%22]%7D%7D%7D"
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'device': 'CITIZEN',
                'origin': 'https://beitakfemisr.com',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'],
                'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }

            response = requests.get(url, headers=headers)
            self.log(f'استجابة API للخدمات: {response.status_code}')

            if response.status_code == 200:
                data = response.json()
                self.log(f'تم استلام بيانات الخدمات: {len(data) if data else 0} فئة')

                self.services = self.extract_active_services(data)

                self.service_combo.clear()
                for service in self.services:
                    ar_label = service.get('translations', {}).get('ar', {}).get('serviceLabel', service.get('serviceLabel', 'خدمة غير معروفة'))
                    self.service_combo.addItem(ar_label, service)

                self.log(f'✅ تم تحميل {len(self.services)} خدمة')
            else:
                self.log(f'❌ فشل تحميل الخدمات: كود الاستجابة {response.status_code}')
                try:
                    error_data = response.json()
                    self.log(f'تفاصيل الخطأ: {error_data}')
                except:
                    self.log(f'نص الاستجابة: {response.text[:200]}')

        except Exception as e:
            self.log(f'❌ خطأ في تحميل الخدمات: {str(e)}')
            import traceback
            self.log(f'تفاصيل الخطأ: {traceback.format_exc()}')
    
    def extract_active_services(self, data):
        services = []
        for category in data:
            if 'services' in category:
                services.extend(category['services'])
                # for service in category['services']:
                #     if not service.get('deactivated', False):
                #         services.append(service)
        return services
    
    def on_service_changed(self):
        if self.service_combo.currentIndex() < 0:
            self.log('❌ لم يتم اختيار خدمة صحيحة')
            return

        service = self.service_combo.currentData()
        if service:
            service_name = service.get('translations', {}).get('ar', {}).get('serviceLabel', service.get('serviceLabel', 'غير معروف'))
            self.log(f'🔄 تم اختيار الخدمة: {service_name} ({service["serviceSlug"]})')

            # Clear all dependent fields first
            self.model_combo.clear()
            self.building_combo.clear()
            self.floor_combo.clear()
            self.unit_combo.clear()
            self.alternative_combo.clear()
            self.reservation_combo.clear()

            # Load data for the selected service
            self.load_alternative_completions(service['serviceSlug'])
            self.load_field_options('model')
            self.load_field_options('reservationRequest')
        else:
            self.log('❌ لا توجد بيانات للخدمة المحددة')
    
    def load_alternative_completions(self, service_slug):
        if not self.is_authenticated():
            self.log('❌ لم يتم تسجيل الدخول بشكل صحيح')
            return

        self.log(f'جاري تحميل خيارات الاستكمال للخدمة: {service_slug}')

        try:
            url = f"https://api.beitakfemisr.com/api/dynamic_services/findOne?filter=%7B%22where%22:%7B%22serviceSlug%22:%22{service_slug}%22%7D,%22fields%22:%7B%22workflow%22:true,%22serviceLabel%22:true,%22serviceSlug%22:true,%22serviceType%22:true,%22translations%22:true,%22hooks%22:true,%22serviceDictionary%22:true,%22tour%22:true,%22fromDate%22:true,%22toDate%22:true,%22deactivated%22:true,%22paymentInAdvance%22:true,%22hideServiceTable%22:true,%22disabledOn%22:true%7D%7D&activitiesLimit=1"

            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'origin': 'https://beitakfemisr.com',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'],
                'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }

            response = requests.get(url, headers=headers)
            self.log(f'استجابة API للاستكمال: {response.status_code}')

            if response.status_code == 200:
                data = response.json()
                self.log(f'تم استلام البيانات: {len(data) if data else 0} عنصر')

                # Validate response structure
                if not data or len(data['activities']) == 0:
                    self.log('❌ لا توجد بيانات في الاستجابة')
                    return

                if len(data['activities']) == 0:
                    self.log('❌ لا توجد خدمات في الاستجابة')
                    return

                # Navigate through the response structure safely
                service_data = data['activities'][0]['steps'][0]
                if 'fieldsSchema' not in service_data:
                    self.log('❌ لا يوجد fieldsSchema في البيانات')
                    return

                if 'selectUnit' not in service_data['fieldsSchema']:
                    self.log('❌ لا يوجد selectUnit في fieldsSchema')
                    return

                if 'schema' not in service_data['fieldsSchema']['selectUnit']:
                    self.log('❌ لا يوجد schema في selectUnit')
                    return

                if 'alternativeCompletion' not in service_data['fieldsSchema']['selectUnit']['schema']:
                    self.log('❌ لا يوجد alternativeCompletion في schema')
                    return

                alternatives = service_data['fieldsSchema']['selectUnit']['schema']['alternativeCompletion']

                self.alternative_combo.clear()

                if 'uniforms' in alternatives and 'translations' in alternatives['uniforms'] and 'ar' in alternatives['uniforms']['translations']:
                    translations = alternatives['uniforms']['translations']['ar']
                    options = alternatives['uniforms'].get('options', [])

                    for option in options:
                        label = translations.get(option['label'], option['label'])
                        self.alternative_combo.addItem(label, option['value'])

                    self.log(f'✅ تم تحميل {len(options)} خيار للاستكمال')
                else:
                    self.log('❌ بنية البيانات غير صحيحة للاستكمال')
            else:
                self.log(f'❌ فشل تحميل خيارات الاستكمال: كود الاستجابة {response.status_code}')
                try:
                    error_data = response.json()
                    self.log(f'تفاصيل الخطأ: {error_data}')
                except:
                    self.log(f'نص الاستجابة: {response.text[:200]}')

        except Exception as e:
            self.log(f'❌ خطأ في تحميل خيارات الاستكمال: {str(e)}')
            import traceback
            self.log(f'تفاصيل الخطأ: {traceback.format_exc()}')
    
    def load_field_options(self, field_id):
        service = self.service_combo.currentData()
        if not service:
            self.log(f'❌ لا توجد خدمة محددة لتحميل {field_id}')
            return

        if not self.is_authenticated():
            self.log('❌ لم يتم تسجيل الدخول بشكل صحيح')
            return

        self.log(f'جاري تحميل خيارات {field_id}...')

        try:
            url = f"https://api.beitakfemisr.com/api/{service['serviceSlug']}/fieldHandler"

            data = {"fieldId": field_id, "data": {"apiDependencies": {}}, "activityId": "unitSelect"}

            # Add dependencies based on field
            if field_id == 'buildingNumber' and self.model_combo.currentData():
                data['data']['model'] = self.model_combo.currentData()
                self.log(f'إضافة تبعية النموذج: {self.model_combo.currentData()}')
            elif field_id == 'floor':
                if self.model_combo.currentData():
                    data['data']['model'] = self.model_combo.currentData()
                    self.log(f'إضافة تبعية النموذج: {self.model_combo.currentData()}')
                if self.building_combo.currentData():
                    data['data']['buildingNumber'] = self.building_combo.currentData()
                    self.log(f'إضافة تبعية المبنى: {self.building_combo.currentData()}')
            elif field_id == 'unitNumber':
                if self.model_combo.currentData():
                    data['data']['model'] = self.model_combo.currentData()
                    self.log(f'إضافة تبعية النموذج: {self.model_combo.currentData()}')
                if self.building_combo.currentData():
                    data['data']['buildingNumber'] = self.building_combo.currentData()
                    self.log(f'إضافة تبعية المبنى: {self.building_combo.currentData()}')
                if self.floor_combo.currentData():
                    data['data']['floor'] = self.floor_combo.currentData()
                    self.log(f'إضافة تبعية الدور: {self.floor_combo.currentData()}')

            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'content-type': 'application/json',
                'origin': 'https://beitakfemisr.com',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'],
                'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }

            self.log(f'إرسال طلب إلى: {url}')
            self.log(f'بيانات الطلب: {data}')

            response = requests.post(url, headers=headers, json=data)
            self.log(f'استجابة API لـ {field_id}: {response.status_code}')

            if response.status_code == 200:
                options = response.json()
                self.log(f'تم استلام {len(options) if isinstance(options, list) else "غير معروف"} خيار لـ {field_id}')

                combo_map = {
                    'model': self.model_combo,
                    'buildingNumber': self.building_combo,
                    'floor': self.floor_combo,
                    'unitNumber': self.unit_combo,
                    'reservationRequest': self.reservation_combo
                }

                combo = combo_map.get(field_id)

                if combo is None:
                    self.log(f'❌ لا يوجد combo box لـ {field_id}')
                    self.log(f'Available combo boxes: {list(combo_map.keys())}')
                    self.log(f'Requested field_id: "{field_id}" (type: {type(field_id)})')
                    return

                # Check if combo is valid and not None
                self.log(f'combo for {field_id}: {combo} (type: {type(combo)}, bool: {bool(combo)})')

                if combo is not None:
                    combo.clear()

                    if isinstance(options, list):
                        for option in options:
                            if isinstance(option, dict) and 'label' in option and 'value' in option:
                                # cast label to string
                                combo.addItem(str(option['label']), option['value'])
                                
                                # combo.addItem(option['label'], option['value'])
                            else:
                                self.log(f'❌ خيار غير صحيح: {option}')

                        self.log(f'✅ تم تحميل {len(options)} خيار لـ {field_id}')
                    else:
                        self.log(f'❌ تنسيق الاستجابة غير صحيح لـ {field_id}: {type(options)}')
                else:
                    self.log(f'❌ combo box لـ {field_id} is None or invalid')
            else:
                self.log(f'❌ فشل تحميل خيارات {field_id}: كود الاستجابة {response.status_code}')
                try:
                    error_data = response.json()
                    self.log(f'تفاصيل الخطأ: {error_data}')
                except:
                    self.log(f'نص الاستجابة: {response.text[:200]}')

        except Exception as e:
            self.log(f'❌ خطأ في تحميل خيارات {field_id}: {str(e)}')
            import traceback
            self.log(f'تفاصيل الخطأ: {traceback.format_exc()}')
    
    def add_attempt(self):
        service = self.service_combo.currentData()
        if not service:
            QMessageBox.warning(self, 'خطأ', 'الرجاء اختيار خدمة')
            return
        
        if not all([self.model_combo.currentData(), self.building_combo.currentData(),
                   self.floor_combo.currentData(), self.unit_combo.currentData(),
                   self.alternative_combo.currentData(), self.reservation_combo.currentData()]):
            QMessageBox.warning(self, 'خطأ', 'الرجاء اختيار جميع الحقول')
            return
        
        attempt = {
            'service_slug': service['serviceSlug'],
            'model': self.model_combo.currentData(),
            'buildingNumber': self.building_combo.currentData(),
            'floor': self.floor_combo.currentData(),
            'unitNumber': self.unit_combo.currentData(),
            'alternativeCompletion': self.alternative_combo.currentData(),
            'reservationRequest': self.reservation_combo.currentData(),
            'display': f"{self.model_combo.currentText()} - {self.building_combo.currentText()} - {self.floor_combo.currentText()} - {self.unit_combo.currentText()} - {self.alternative_combo.currentText()}"
        }
        
        self.attempts.append(attempt)
        self.attempts_list.addItem(attempt['display'])
        self.log(f'✅ تمت إضافة محاولة: {attempt["display"]}')
    
    def remove_attempt(self):
        current_row = self.attempts_list.currentRow()
        if current_row >= 0:
            self.attempts.pop(current_row)
            self.attempts_list.takeItem(current_row)
            self.log('✅ تم حذف المحاولة')
    
    def start_booking(self):
        if not self.attempts:
            QMessageBox.warning(self, 'خطأ', 'الرجاء إضافة محاولة واحدة على الأقل')
            return
        
        service = self.service_combo.currentData()
        self.worker = BookingWorker(self.attempts, self.delay_spin.value(), 
                                    self.auth_data, service['serviceSlug'])
        self.worker.log_signal.connect(self.log)
        self.worker.finished.connect(self.on_booking_finished)
        self.worker.start()
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.log('🚀 بدء عملية الحجز...')
    
    def stop_booking(self):
        if self.worker:
            self.worker.stop()
            self.log('⏹️ تم إيقاف عملية الحجز')
    
    def on_booking_finished(self):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.log('انتهت عملية الحجز')

    def debug_unit_details(self):
        """Debug method to get and display unit details without booking"""
        service = self.service_combo.currentData()
        if not service:
            QMessageBox.warning(self, 'خطأ', 'الرجاء اختيار خدمة')
            return

        if not all([self.model_combo.currentData(), self.building_combo.currentData(),
                   self.floor_combo.currentData(), self.unit_combo.currentData(),
                   self.alternative_combo.currentData()]):
            QMessageBox.warning(self, 'خطأ', 'الرجاء اختيار جميع الحقول المطلوبة')
            return

        if not self.is_authenticated():
            QMessageBox.warning(self, 'خطأ', 'لم يتم تسجيل الدخول بشكل صحيح')
            return

        self.log('🔍 جاري اختبار الحصول على تفاصيل الوحدة...')

        try:
            # Create attempt data
            attempt = {
                'model': self.model_combo.currentData(),
                'buildingNumber': self.building_combo.currentData(),
                'floor': self.floor_combo.currentData(),
                'unitNumber': self.unit_combo.currentData(),
                'alternativeCompletion': self.alternative_combo.currentData()
            }

            # Get unit details using the same method as BookingWorker
            url = f"https://api.beitakfemisr.com/api/{service['serviceSlug']}/fieldHandler"
            payload = {
                "fieldId": "selectUnit",
                "data": {
                    "model": attempt['model'],
                    "buildingNumber": attempt['buildingNumber'],
                    "floor": attempt['floor'],
                    "unitNumber": attempt['unitNumber'],
                    "alternativeCompletion": attempt['alternativeCompletion']
                },
                "activityId": "unitSelect"
            }

            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'content-type': 'application/json',
                'origin': 'https://beitakfemisr.com',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'],
                'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }

            response = requests.post(url, headers=headers, json=payload)

            if response.status_code == 200:
                unit_details = response.json()
                self.log('✅ تم الحصول على تفاصيل الوحدة بنجاح')

                # Format the details for display
                details_text = "تفاصيل الوحدة:\n\n"
                details_text += f"ID: {unit_details.get('id', 'غير متوفر')}\n"
                details_text += f"المدينة: {unit_details.get('city', 'غير متوفر')}\n"
                details_text += f"المنطقة: {unit_details.get('region', 'غير متوفر')}\n"
                details_text += f"النموذج: {unit_details.get('model', 'غير متوفر')}\n"
                details_text += f"رقم المبنى: {unit_details.get('buildingNumber', 'غير متوفر')}\n"
                details_text += f"الدور: {unit_details.get('floor', 'غير متوفر')}\n"
                details_text += f"رقم الوحدة: {unit_details.get('unitNumber', 'غير متوفر')}\n"
                details_text += f"نوع الوحدة: {unit_details.get('unitType', 'غير متوفر')}\n"
                details_text += f"كود الوحدة: {unit_details.get('unitCode', 'غير متوفر')}\n"
                details_text += f"المساحة: {unit_details.get('area', 'غير متوفر')} متر مربع\n"
                details_text += f"عدد الغرف: {unit_details.get('roomCount', 'غير متوفر')}\n"
                details_text += f"التشطيب: {unit_details.get('finishing', 'غير متوفر')}\n"
                details_text += f"سعر المتر: {unit_details.get('pricePerMeter', 'غير متوفر')} جنيه\n"
                details_text += f"رسوم الحجز المتبقية (دولار): {unit_details.get('unitRemainingReservationFeesUSD', 'غير متوفر')}\n"
                details_text += f"الرسوم الإدارية (دولار): {unit_details.get('adminFeesUSD', 'غير متوفر')}\n"
                details_text += f"نسبة التميز: {unit_details.get('excellencePercentages', 'غير متوفر')}\n"
                details_text += f"الاستكمال البديل: {unit_details.get('alternativeCompletion', 'غير متوفر')}\n"

                # Log the raw JSON response
                self.log(f'البيانات الخام: {json.dumps(unit_details, ensure_ascii=False, indent=2)}')

                # Show details in a message box
                msg = QMessageBox()
                msg.setWindowTitle('تفاصيل الوحدة')
                msg.setText(details_text)
                msg.setDetailedText(f"البيانات الخام (JSON):\n{json.dumps(unit_details, ensure_ascii=False, indent=2)}")
                msg.exec_()

            else:
                error_msg = f'❌ فشل الحصول على تفاصيل الوحدة: كود الاستجابة {response.status_code}'
                self.log(error_msg)
                try:
                    error_data = response.json()
                    self.log(f'تفاصيل الخطأ: {error_data}')
                    QMessageBox.critical(self, 'خطأ', f'{error_msg}\n\nتفاصيل الخطأ:\n{json.dumps(error_data, ensure_ascii=False, indent=2)}')
                except:
                    self.log(f'نص الاستجابة: {response.text[:200]}')
                    QMessageBox.critical(self, 'خطأ', f'{error_msg}\n\nنص الاستجابة:\n{response.text[:500]}')

        except Exception as e:
            error_msg = f'❌ خطأ في اختبار تفاصيل الوحدة: {str(e)}'
            self.log(error_msg)
            import traceback
            self.log(f'تفاصيل الخطأ: {traceback.format_exc()}')
            QMessageBox.critical(self, 'خطأ', f'{error_msg}\n\nتفاصيل الخطأ:\n{traceback.format_exc()}')

    def log(self, message):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f'[{timestamp}] {message}'
        self.log_text.append(log_message)

        # Log to file
        with open('booking_log.txt', 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')


if __name__ == '__main__':
    elitesoftworks.print_logo('1.0', 'beitakfemisr requests')
    app = QApplication(sys.argv)
    
    # Set font for Arabic support
    font = QFont()
    font.setFamily('Arial')
    font.setPointSize(10)
    app.setFont(font)
    
    window = BookingBot()
    window.show()
    sys.exit(app.exec_())